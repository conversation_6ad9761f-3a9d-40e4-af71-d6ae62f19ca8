/**
 * @fileoverview Business layer service for handling profile functionality
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { computed, inject, Injectable, signal } from '@angular/core';
import { RequestState } from 'lib/lib-app-core/src/lib/utils/request-state';
import { CookieService } from 'lib/lib-app-core/src/lib/services/cookie-service/cookie.service';
import { ProfileDL } from '../data-layer/profile-dl.service';
import { UserLoginDTOModel } from 'lib/lib-auth/src/lib/models/user-login-dto.model';
import { map } from 'rxjs';
import { ProfileData, ProfileApiResponse } from '../../interfaces/profile.interface';

@Injectable()
export class ProfileBL extends RequestState {
    private _profileDL = inject(ProfileDL);
    private _cookieService = inject(CookieService);
    
    // Profile data signal
    profileData = signal<ProfileData | null>(null);
    
    // Computed property to check if profile data is loaded
    readonly isProfileLoaded = computed(() => this.profileData() !== null);

    /**
     * Loads user profile data by getting userId from cookies,
     * building API parameters, and handling the request state.
     */
    loadProfile(): void {
        const userId = this._cookieService.getCookie('userId');
        
        if (!userId) {
            this.errorMessage.set('User ID not found in cookies');
            return;
        }

        this._profileDL.buildApiParams({ guid: userId });
        const profile$ = this._profileDL.load().pipe(
            map((response: ProfileApiResponse) => {
                const userData = response.data?.[0];
                if (!userData) {
                    throw new Error('No user data found in response');
                }
                return this.mapUserDataToProfile(userData);
            })
        );

        this._handleRequest(profile$, (profileData: ProfileData) => {
            this.profileData.set(profileData);
        });
    }

    /**
     * Maps UserLoginDTOModel to ProfileData interface
     * @param userData - The user data from API response
     * @returns ProfileData - Mapped profile data
     */
    private mapUserDataToProfile(userData: UserLoginDTOModel): ProfileData {
        return {
            firstName: userData.FirstName || '',
            lastName: userData.LastName || '',
            dateOfBirth: userData.DateOfBirth || '',
            email: userData.Email || '',
            mobileNumber: userData.PhoneNumber || '',
            countryCode: '+1' // Default country code, could be extracted from ContactDTOList if needed
        };
    }

    /**
     * Resets the profile data
     */
    resetProfile(): void {
        this.profileData.set(null);
        this.errorMessage.set(null);
    }
}
