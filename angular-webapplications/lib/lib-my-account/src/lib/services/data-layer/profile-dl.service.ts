/**
 * @fileoverview Data layer service for handling profile API calls
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { Injectable } from '@angular/core';
import { ApiServiceBase } from 'lib-app-core';
import { Observable } from 'rxjs';
import { ProfileApiParams, ProfileApiResponse } from '../../interfaces/profile.interface';

@Injectable()
export class ProfileDL extends ApiServiceBase {
    private _apiParams!: ProfileApiParams;

    constructor() {
        super('profile_data', 'PRIMARY_RELATION');
        this.init();
    }

    buildApiParams(data: ProfileApiParams) {
        this._apiParams = data;
    }

    /**
     * Loads user profile data from the API
     * This method constructs the API URL with the provided GUID and
     * makes an HTTP GET request to fetch user profile details.
     * 
     * @returns Observable<ProfileApiResponse> - User profile response
     */
    load(): Observable<ProfileApiResponse> {
        const { guid } = this._apiParams;
        const url = this.getApiUrl().replace('{CustomerId}', guid);
        
        return this._http.get<ProfileApiResponse>(url);
    }
}
