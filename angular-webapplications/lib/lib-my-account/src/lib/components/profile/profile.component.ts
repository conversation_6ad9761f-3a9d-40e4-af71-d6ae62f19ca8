import { AsyncPipe, CommonModule, DatePipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DATE_FORMAT, DatePickerComponent, ErrorMessage, PhoneInputComponent, TextInputComponent } from 'lib-ui-kit';
import { CustomerRelationBL } from 'projects/online-waiver/src/app/services/business-layer/customer-relation-bl.service';
import { PrimaryCustomerDL } from 'projects/online-waiver/src/app/services/data-layer/primary-customer-dl.service';
import { RelatedCustomerDL } from 'projects/online-waiver/src/app/services/data-layer/related-customer-dl.service';
import { switchMap } from 'rxjs';

interface ProfileFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
    email: FormControl<string | null>;
    mobileNumber: FormControl<string | null>;
}

@Component({
    selector: 'lib-profile',
    imports: [
        CommonModule,
        AsyncPipe,
        DatePipe,
        ReactiveFormsModule,
        TextInputComponent,
        DatePickerComponent,
        PhoneInputComponent,
    ],
    providers: [CustomerRelationBL, PrimaryCustomerDL, RelatedCustomerDL],
    templateUrl: './profile.component.html',
    styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit {
    protected readonly dateFormat = DATE_FORMAT.YYYY_MM_DD;
    private readonly _customerRelationBL = inject(CustomerRelationBL);
    private readonly _fb = inject(FormBuilder);

    // Follow the same pattern as other components using CustomerRelationBL
    primaryParticipant$ = this._customerRelationBL._primaryTrigger.pipe(
        switchMap(() => this._customerRelationBL.getPrimaryCustomerData())
    );

    isEditMode = false;
    profileForm: FormGroup;

    // Error messages for form validation
    errorMessages: ErrorMessage<ProfileFormData> = {
        email: {
            required: 'Email is required',
            email: 'Please enter a valid email address',
        },
        mobileNumber: {
            required: 'Mobile number is required',
        },
        firstName: {
            required: 'First name is required',
        },
        lastName: {
            required: 'Last name is required',
        },
        dateOfBirth: {
            required: 'Date of birth is required',
        },
    };

    constructor() {
        // Initialize form with empty values - will be populated when data loads
        this.profileForm = this._fb.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            dateOfBirth: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            mobileNumber: ['', Validators.required],
        });
    }

    ngOnInit(): void {
        // Trigger loading of primary customer data
        this._customerRelationBL.refetchPrimaryCustomerData();
    }

    toggleEditMode(): void {
        this.isEditMode = !this.isEditMode;

        if (this.isEditMode) {
            // Reset form with current profile values when entering edit mode
            // Get the current primary participant data from the service
            const primaryParticipant = this._customerRelationBL.primaryParticipant();
            if (primaryParticipant?.data) {
                this.profileForm.patchValue({
                    firstName: primaryParticipant.data.firstName,
                    lastName: primaryParticipant.data.lastName,
                    dateOfBirth: primaryParticipant.data.dateOfBirth,
                    email: primaryParticipant.data.email,
                    mobileNumber: primaryParticipant.data.phoneNumber,
                });
            }
        }
    }

    updateProfile(): void {
        if (this.profileForm.valid) {
            // Get form values
            const formValues = this.profileForm.value;

            // Exit edit mode
            this.isEditMode = false;

            // In a real app, you would save to backend here
            console.log('Profile updated:', formValues);

            // TODO: Implement profile update API call
            // For now, just log the updated values
        } else {
            // Mark all fields as touched to show validation errors
            this.profileForm.markAllAsTouched();
        }
    }

    cancelEdit(): void {
        // Exit edit mode without saving changes
        this.isEditMode = false;
    }

    retryLoadProfile(): void {
        // Retry loading profile data
        this._customerRelationBL.refetchPrimaryCustomerData();
    }
}
