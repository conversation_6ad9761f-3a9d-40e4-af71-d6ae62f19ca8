import { CommonModule } from '@angular/common';
import { Component, computed, inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DATE_FORMAT, DatePickerComponent, ErrorMessage, PhoneInputComponent, TextInputComponent } from 'lib-ui-kit';
import { ProfileBL } from '../../services/business-layer/profile-bl.service';
import { ProfileDL } from '../../services/data-layer/profile-dl.service';

interface ProfileFormData {
    firstName: FormControl<string | null>;
    lastName: FormControl<string | null>;
    dateOfBirth: FormControl<string | null>;
    email: FormControl<string | null>;
    mobileNumber: FormControl<string | null>;
}

@Component({
    selector: 'lib-profile',
    imports: [
        CommonModule,
        ReactiveFormsModule,
        TextInputComponent,
        DatePickerComponent,
        PhoneInputComponent,
    ],
    providers: [ProfileBL, ProfileDL],
    templateUrl: './profile.component.html',
    styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit {
    protected readonly dateFormat = DATE_FORMAT.YYYY_MM_DD;
    private readonly _profileBL = inject(ProfileBL);
    private readonly _fb = inject(FormBuilder);

    // Computed properties for reactive data
    readonly profileData = computed(() => this._profileBL.profileData());
    readonly loading = computed(() => this._profileBL.loading());
    readonly errorMessage = computed(() => this._profileBL.errorMessage());
    readonly isProfileLoaded = computed(() => this._profileBL.isProfileLoaded());

    isEditMode = false;
    profileForm: FormGroup;

    // Error messages for form validation
    errorMessages: ErrorMessage<ProfileFormData> = {
        email: {
            required: 'Email is required',
            email: 'Please enter a valid email address',
        },
        mobileNumber: {
            required: 'Mobile number is required',
        },
        firstName: {
            required: 'First name is required',
        },
        lastName: {
            required: 'Last name is required',
        },
        dateOfBirth: {
            required: 'Date of birth is required',
        },
    };

    constructor() {
        // Initialize form with empty values - will be populated when data loads
        this.profileForm = this._fb.group({
            firstName: ['', Validators.required],
            lastName: ['', Validators.required],
            dateOfBirth: ['', Validators.required],
            email: ['', [Validators.required, Validators.email]],
            mobileNumber: ['', Validators.required],
        });
    }

    ngOnInit(): void {
        // Load profile data on component initialization
        this._profileBL.loadProfile();
    }

    toggleEditMode(): void {
        this.isEditMode = !this.isEditMode;

        if (this.isEditMode) {
            // Reset form with current profile values when entering edit mode
            const currentProfile = this.profileData();
            if (currentProfile) {
                this.profileForm.patchValue({
                    firstName: currentProfile.firstName,
                    lastName: currentProfile.lastName,
                    dateOfBirth: currentProfile.dateOfBirth,
                    email: currentProfile.email,
                    mobileNumber: currentProfile.mobileNumber,
                });
            }
        }
    }

    updateProfile(): void {
        if (this.profileForm.valid) {
            // Get form values
            const formValues = this.profileForm.value;

            // Exit edit mode
            this.isEditMode = false;

            // In a real app, you would save to backend here
            console.log('Profile updated:', formValues);

            // TODO: Implement profile update API call
            // For now, just log the updated values
        } else {
            // Mark all fields as touched to show validation errors
            this.profileForm.markAllAsTouched();
        }
    }

    cancelEdit(): void {
        // Exit edit mode without saving changes
        this.isEditMode = false;
    }

    retryLoadProfile(): void {
        // Retry loading profile data
        this._profileBL.loadProfile();
    }
}
