<div class="bg-transparent md:bg-surface-white md:rounded-4xl h-full md:h-[calc(100vh-168px)]">
    <!-- Profile Title (Mobile Only) -->
    <h1 i18n="profile.title" class="text-lg font-semibold text-primary mb-5 md:hidden">My Profile</h1>

    <!-- Profile Card -->
    <div class="h-full bg-surface-white shadow-lg p-6 mb-8 relative rounded-4xl">
        <!-- Title and Edit <PERSON> (Desktop) -->
        <div class="hidden md:flex items-center justify-between mb-6">
            <h1 i18n="profile.title" class="text-lg font-semibold text-primary">My Profile</h1>
            @if (!isEditMode) {
            <button (click)="toggleEditMode()" class="text-secondary-blue hover:text-secondary-blue-dark">
                <img src="assets/icons/edit.svg" alt="Edit" />
            </button>
            }
        </div>

        <!-- <PERSON> (Mobile) -->
        @if (!isEditMode) {
        <button (click)="toggleEditMode()" class="absolute top-6 right-6 md:hidden">
            <img src="assets/icons/edit.svg" alt="Edit" />
        </button>
        }

        <!-- Loading State -->
        @if (loading()) {
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-6 bg-gray-300 rounded"></div>
            </div>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-6 bg-gray-300 rounded"></div>
            </div>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-6 bg-gray-300 rounded"></div>
            </div>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-6 bg-gray-300 rounded"></div>
            </div>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded mb-2"></div>
                <div class="h-6 bg-gray-300 rounded"></div>
            </div>
        </div>
        } @else if (errorMessage()) {
        <!-- Error State -->
        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-red-600 font-medium">Error loading profile</p>
            <p class="text-red-500 text-sm">{{ errorMessage() }}</p>
            <button (click)="retryLoadProfile()" class="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                Retry
            </button>
        </div>
        } @else if (!isEditMode && profileData()) {
        <!-- View Mode -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-6">
            <div>
                <p i18n="profile.firstName" class="text-xs text-neutral-dark mb-1">First Name</p>
                <p class="text-primary">{{ profileData()?.firstName || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.lastName" class="text-xs text-neutral-dark mb-1">Last Name</p>
                <p class="text-primary">{{ profileData()?.lastName || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.dateOfBirth" class="text-xs text-neutral-dark mb-1">Date of Birth</p>
                <p class="text-primary">{{ (profileData()?.dateOfBirth | date: 'yyyy-MM-dd') || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.emailAddress" class="text-xs text-neutral-dark mb-1">Email Address</p>
                <p class="text-primary">{{ profileData()?.email || 'N/A' }}</p>
            </div>

            <div>
                <p i18n="profile.mobileNumber" class="text-xs text-neutral-dark mb-1">Mobile Number</p>
                <p class="text-primary">
                    {{ profileData()?.countryCode || '' }} {{ profileData()?.mobileNumber || 'N/A' }}
                </p>
            </div>
        </div>
        }

        <!-- Edit Mode -->
        @if (isEditMode) {
        <form [formGroup]="profileForm" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- First Name -->
            <div>
                <lib-text-input formControlName="firstName" name="firstName" label="First Name"
                    [errorMessages]="errorMessages.firstName"></lib-text-input>
            </div>

            <!-- Last Name -->
            <div>
                <lib-text-input formControlName="lastName" name="lastName" label="Last Name"
                    [errorMessages]="errorMessages.lastName"></lib-text-input>
            </div>

            <!-- Date of Birth -->
            <div>
                <lib-date-picker formControlName="dateOfBirth" name="dateOfBirth" label="Date of Birth"
                    [placeholder]="dateFormat.toUpperCase()" [format]="dateFormat" [rightOffset]="0"
                    [errorMessages]="errorMessages.dateOfBirth"></lib-date-picker>
            </div>

            <!-- Email Address -->
            <div>
                <lib-text-input formControlName="email" name="email" label="Email Address" type="email"
                    [errorMessages]="errorMessages.email"></lib-text-input>
            </div>

            <!-- Mobile Number -->
            <div>
                <lib-phone-input formControlName="mobileNumber" name="mobileNumber" label="Mobile Number"
                    [countries]="[]"
                    [errorMessages]="errorMessages.mobileNumber"></lib-phone-input>
            </div>

            <!-- Action Buttons -->
            <div class="col-span-full mt-4 pt-4 border-t border-neutral-light">
                <div class="flex flex-col sm:flex-row gap-4 lg:w-[60%]">
                    <button type="button" (click)="updateProfile()" [disabled]="profileForm.invalid"
                        class="w-full py-3 px-4 bg-primary text-white font-medium rounded-full disabled:bg-surface">
                        Update
                    </button>
                    <button type="button" (click)="cancelEdit()"
                        class="w-full py-3 px-4 bg-surface-white text-black border border-primary font-medium rounded-full">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
        }
    </div>
</div>