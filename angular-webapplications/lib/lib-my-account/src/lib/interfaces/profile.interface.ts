/**
 * @fileoverview TypeScript interfaces for profile functionality
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { UserLoginDTOModel } from 'lib/lib-auth/src/lib/models/user-login-dto.model';

/**
 * Profile data interface for component usage
 */
export interface ProfileData {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    email: string;
    mobileNumber: string;
    countryCode: string;
}

/**
 * Profile API parameters interface
 */
export interface ProfileApiParams {
    guid: string;
}

/**
 * Profile API response interface
 */
export interface ProfileApiResponse {
    data: UserLoginDTOModel[];
}

/**
 * Profile state interface for component consumption
 */
export interface ProfileState {
    loading: boolean;
    error: string | null;
    profileData: ProfileData | null;
    isProfileLoaded: boolean;
}

/**
 * Profile form data interface matching the component's form structure
 */
export interface ProfileFormData {
    firstName: string | null;
    lastName: string | null;
    dateOfBirth: string | null;
    email: string | null;
    mobileNumber: string | null;
}
